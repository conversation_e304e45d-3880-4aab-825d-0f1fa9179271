'use client';

import React from 'react';
import {
  ReadingProgress,
  ArticleInteractions,
  FocusMode,
} from '@/components/blog';
import { BlogPost } from '@/types';

interface BlogPostClientProps {
  post: BlogPost;
  siteUrl: string;
  locale: string;
  userId?: string; // 当前登录用户ID
}

export function BlogPostClient({ 
  post,
  userId
}: BlogPostClientProps) {
  return (
    <>
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />



      {/* 右侧浮动工具栏 - 仅保留核心功能 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <ArticleInteractions
          postId={post.id}
          initialLikes={post.likeCount}
          initialViews={post.viewCount}
          initialComments={post.commentCount}
          initialShares={post.shareCount}
          userId={userId}
          variant="floating"
          onShare={(platform) => {
            // 可以在这里添加额外的分享逻辑
            console.log(`Shared to ${platform}`);
          }}
        />
      </div>

      {/* 专注模式控制 - 右下角 */}
      <FocusMode className="fixed right-6 bottom-6 z-50" />

    </>
  );
}
